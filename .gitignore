# ─────────────────────────────────────────
# ✨ Общие мусорные файлы
# ─────────────────────────────────────────
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# ─────────────────────────────────────────
# 🧠 Dart / Flutter / Pub
# ─────────────────────────────────────────
.dart_tool/
.packages
.pub-cache/
.pub/
.build/
build/
.flutter-plugins
.flutter-plugins-dependencies
.generated/
**/doc/api/
**/ios/Flutter/.last_build_id

# Symbolication & obfuscation
app.*.symbols
app.*.map.json

# ─────────────────────────────────────────
# 🖥 IDE и редакторы
# ─────────────────────────────────────────

# IntelliJ / Android Studio
.idea/
*.iml
*.ipr
*.iws

# VS Code (оставь закомментированным, если хочешь сохранить настройки проекта)
#.vscode/

# ─────────────────────────────────────────
# 🤖 Android
# ─────────────────────────────────────────
android/.gradle/
android/local.properties
android/app/debug/
android/app/profile/
android/app/release/

# ─────────────────────────────────────────
# 🍎 iOS
# ─────────────────────────────────────────
ios/.symlinks/
ios/Flutter/Generated.xcconfig
ios/Flutter/flutter_export_environment.sh
ios/Flutter/Debug.xcconfig
ios/Flutter/Release.xcconfig
ios/Pods/
ios/Runner.xcworkspace/
Podfile.lock

# ─────────────────────────────────────────
# 💻 macOS
# ─────────────────────────────────────────
macos/Flutter/ephemeral/
macos/Flutter/GeneratedPluginRegistrant.swift
macos/Flutter/flutter_export_environment.sh
macos/Runner.xcworkspace/

# ─────────────────────────────────────────
# 🪟 Windows
# ─────────────────────────────────────────
windows/flutter/ephemeral/
windows/.vs/

# ─────────────────────────────────────────
# 🐧 Linux
# ─────────────────────────────────────────
linux/flutter/ephemeral/


# ─────────────────────────────────────────
# 🧼 Дополнительно (удобство и безопасность)
# ─────────────────────────────────────────
.AppleDouble
.LSOverride
._*
*~
*.bak
*.tmp
node_modules/
*.keystore
*.jks
*.pem
*.p12
*.crt
*.key
coverage/
firebase-debug.log
*.orig