import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:medstata_app/medstata_api.dart';

void main() {
  group('LabReportDetailScreen Tests', () {
    testWidgets('LabReportDetailScreen should build without errors', (WidgetTester tester) async {
      // Создаем тестовое приложение с экраном
      await tester.pumpWidget(
        MaterialApp(
          home: const LabReportDetailScreen(
            reportId: 'test_report_id',
          ),
        ),
      );

      // Проверяем, что экран загружается
      expect(find.byType(LabReportDetailScreen), findsOneWidget);
      
      // Проверяем наличие AppBar с заголовком
      expect(find.text('Результаты'), findsOneWidget);
      
      // Проверяем наличие индикатора загрузки
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('LabReportDetailScreen should show loading indicator initially', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const LabReportDetailScreen(
            reportId: 'test_report_id',
          ),
        ),
      );

      // Проверяем, что показывается индикатор загрузки
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('AppBar should have correct title and buttons', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const LabReportDetailScreen(
            reportId: 'test_report_id',
          ),
        ),
      );

      // Проверяем заголовок
      expect(find.text('Результаты'), findsOneWidget);

      // Проверяем кнопку назад
      expect(find.byIcon(Icons.arrow_back_ios), findsOneWidget);

      // Кнопка редактирования показывается только для завершенных отчетов
      // В тестах без реальных данных она не будет показана
    });

    testWidgets('Tab bar should be interactive', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const LabReportDetailScreen(
            reportId: 'test_report_id',
          ),
        ),
      );

      // Ждем завершения загрузки (может показать ошибку)
      await tester.pump(const Duration(seconds: 1));

      // Проверяем наличие табов только если данные загружены
      // В случае ошибки авторизации табы могут не отображаться
      final resultsTab = find.text('Результаты');
      final documentTab = find.text('Документ');

      // Проверяем, что хотя бы один таб найден (в AppBar всегда есть "Результаты")
      expect(resultsTab, findsAtLeastNWidgets(1));

      // Если есть табы, проверяем их наличие
      if (documentTab.evaluate().isNotEmpty) {
        expect(documentTab, findsOneWidget);
      }
    });

    test('Status color mapping should work correctly', () {
      // Создаем экземпляр экрана для тестирования приватных методов
      // Поскольку методы приватные, тестируем логику через публичные методы
      
      // Тестируем различные статусы
      const normalStatus = LabTestStatus.normal;
      const elevatedStatus = LabTestStatus.elevated;
      const lowStatus = LabTestStatus.low;
      const otherStatus = LabTestStatus.other;

      // Проверяем, что статусы определены корректно
      expect(normalStatus, equals(LabTestStatus.normal));
      expect(elevatedStatus, equals(LabTestStatus.elevated));
      expect(lowStatus, equals(LabTestStatus.low));
      expect(otherStatus, equals(LabTestStatus.other));
    });

    test('Display value logic should work correctly', () {
      // Тестируем логику отображения значений
      // Поскольку метод приватный, тестируем логику отдельно

      String getDisplayValue(LabTest test) {
        // Если есть численное значение и единицы измерения, показываем их
        if (test.value.value.isNotEmpty && test.value.unitLabel.isNotEmpty) {
          // Проверяем, является ли значение числом
          final numericValue = double.tryParse(test.value.value);
          if (numericValue != null) {
            return '${test.value.value} ${test.value.unitLabel}';
          }
        }

        // Если нет численного значения, показываем статус
        switch (test.status) {
          case LabTestStatus.normal:
            return 'Не обнаружено';
          case LabTestStatus.elevated:
            return 'Повышен';
          case LabTestStatus.low:
            return 'Понижен';
          case LabTestStatus.other:
            return 'Прочее';
        }
      }

      // Тест с численным значением
      final numericTest = LabTest(
        id: 'test_1',
        name: 'Холестерин',
        status: LabTestStatus.elevated,
        interpretationReasons: [],
        performedAt: '2025-01-15',
        value: LabTestValue(value: '6.13', unitLabel: 'ммоль/л'),
      );
      expect(getDisplayValue(numericTest), equals('6.13 ммоль/л'));

      // Тест без численного значения
      final nonNumericTest = LabTest(
        id: 'test_2',
        name: 'Качественный тест',
        status: LabTestStatus.normal,
        interpretationReasons: [],
        performedAt: '2025-01-15',
        value: LabTestValue(value: 'отрицательный', unitLabel: ''),
      );
      expect(getDisplayValue(nonNumericTest), equals('Не обнаружено'));

      // Тест с повышенным значением
      final elevatedTest = LabTest(
        id: 'test_3',
        name: 'Повышенный тест',
        status: LabTestStatus.elevated,
        interpretationReasons: [],
        performedAt: '2025-01-15',
        value: LabTestValue(value: 'положительный', unitLabel: ''),
      );
      expect(getDisplayValue(elevatedTest), equals('Повышен'));

      // Тест с пониженным значением
      final lowTest = LabTest(
        id: 'test_4',
        name: 'Пониженный тест',
        status: LabTestStatus.low,
        interpretationReasons: [],
        performedAt: '2025-01-15',
        value: LabTestValue(value: 'низкий', unitLabel: ''),
      );
      expect(getDisplayValue(lowTest), equals('Понижен'));
    });

    testWidgets('Back button should work', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: LabReportDetailScreen(
            reportId: 'test_report_id',
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Проверяем, что кнопка назад присутствует в AppBar
      expect(find.byIcon(Icons.arrow_back_ios), findsOneWidget);

      // Проверяем, что AppBar имеет leading widget (кнопку назад)
      final appBar = tester.widget<AppBar>(find.byType(AppBar));
      expect(appBar.leading, isNotNull);
    });

    test('Report ID should be passed correctly', () {
      const testReportId = 'test_report_12345';
      
      // Создаем виджет с тестовым ID
      const screen = LabReportDetailScreen(reportId: testReportId);
      
      // Проверяем, что ID передался корректно
      expect(screen.reportId, equals(testReportId));
    });

    testWidgets('Error state should be displayed correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const LabReportDetailScreen(
            reportId: 'invalid_report_id',
          ),
        ),
      );

      // Ждем завершения загрузки (должна показаться ошибка)
      await tester.pump(const Duration(seconds: 2));

      // Может показаться ошибка загрузки
      // Проверяем, что есть возможность повторить загрузку
      final retryButtons = find.text('Повторить');
      if (retryButtons.evaluate().isNotEmpty) {
        expect(retryButtons, findsOneWidget);
        
        // Проверяем наличие иконки ошибки
        expect(find.byIcon(Icons.error_outline), findsOneWidget);
      }
    });

    testWidgets('Processing state should be displayed correctly', (WidgetTester tester) async {
      // Тестируем состояние обработки
      await tester.pumpWidget(
        MaterialApp(
          home: const LabReportDetailScreen(
            reportId: 'processing_report_id',
          ),
        ),
      );

      // Ждем завершения загрузки
      await tester.pumpAndSettle();

      // Проверяем, что экран создается без краша
      expect(find.byType(LabReportDetailScreen), findsOneWidget);
    });

    testWidgets('Failed state should be displayed correctly', (WidgetTester tester) async {
      // Тестируем состояние ошибки
      await tester.pumpWidget(
        MaterialApp(
          home: const LabReportDetailScreen(
            reportId: 'failed_report_id',
          ),
        ),
      );

      // Ждем завершения загрузки
      await tester.pumpAndSettle();

      // Проверяем, что экран создается без краша
      expect(find.byType(LabReportDetailScreen), findsOneWidget);
    });

    testWidgets('Completed state should show full content', (WidgetTester tester) async {
      // Тестируем завершенное состояние
      await tester.pumpWidget(
        MaterialApp(
          home: const LabReportDetailScreen(
            reportId: 'completed_report_id',
          ),
        ),
      );

      // Ждем завершения загрузки
      await tester.pumpAndSettle();

      // Проверяем, что экран создается без краша
      expect(find.byType(LabReportDetailScreen), findsOneWidget);
    });

    test('Status-based content logic should work correctly', () {
      // Тестируем логику отображения контента в зависимости от статуса

      // Проверяем, что processing и pending обрабатываются одинаково
      expect(LabReportStatus.processing.name, equals('processing'));
      expect(LabReportStatus.pending.name, equals('pending'));
      expect(LabReportStatus.completed.name, equals('completed'));
      expect(LabReportStatus.failed.name, equals('failed'));
    });
  });
}
