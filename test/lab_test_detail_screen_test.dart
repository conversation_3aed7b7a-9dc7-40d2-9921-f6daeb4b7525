import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import '../lib/screens/lab_test_detail_screen.dart';
import '../lib/medstata_api.dart';

void main() {
  group('LabTestDetailScreen Tests', () {
    late LabTest testLabTest;

    setUp(() {
      testLabTest = LabTest(
        id: 'test_id',
        name: 'Тестовый анализ',
        status: LabTestStatus.normal,
        interpretationReasons: ['Тестовая интерпретация'],
        performedAt: '2025-01-15',
        value: LabTestValue(
          value: '5.0',
          unitLabel: 'ед',
          referenceRange: '3.0-7.0',
        ),
      );
    });

    testWidgets('LabTestDetailScreen should build without errors', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: LabTestDetailScreen(labTest: testLabTest),
        ),
      );

      // Проверяем, что экран загружается
      expect(find.byType(LabTestDetailScreen), findsOneWidget);
      expect(find.byType(Scaffold), findsOneWidget);
    });

    testWidgets('LabTestDetailScreen should show loading indicator initially', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: LabTestDetailScreen(labTest: testLabTest),
        ),
      );

      // Проверяем, что показывается индикатор загрузки
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('AppBar should have correct title and back button', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: LabTestDetailScreen(labTest: testLabTest),
        ),
      );

      // Проверяем AppBar
      expect(find.byType(AppBar), findsOneWidget);
      expect(find.text('Тестовый анализ'), findsOneWidget);
      expect(find.byIcon(Icons.arrow_back_ios), findsOneWidget);
    });

    testWidgets('Back button should work', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: LabTestDetailScreen(labTest: testLabTest),
        ),
      );

      await tester.pumpAndSettle();

      // Проверяем, что кнопка назад присутствует в AppBar
      expect(find.byIcon(Icons.arrow_back_ios), findsOneWidget);

      // Проверяем, что AppBar имеет leading widget (кнопку назад)
      final appBar = tester.widget<AppBar>(find.byType(AppBar));
      expect(appBar.leading, isNotNull);
    });

    testWidgets('Status indicator should work correctly', (WidgetTester tester) async {
      // Тестируем различные статусы
      final normalTest = LabTest(
        id: 'normal_test',
        name: 'Нормальный тест',
        status: LabTestStatus.normal,
        interpretationReasons: [],
        performedAt: '2025-01-15',
        value: LabTestValue(value: '5.0', unitLabel: 'ед'),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: LabTestDetailScreen(labTest: normalTest),
        ),
      );

      // Проверяем, что экран создается без ошибок
      expect(find.byType(LabTestDetailScreen), findsOneWidget);
    });

    test('Status color mapping should work correctly', () {
      // Тестируем маппинг цветов статусов
      // Поскольку методы приватные, тестируем логику отдельно
      
      Color getStatusColor(LabTestStatus status) {
        switch (status) {
          case LabTestStatus.normal:
            return Colors.green;
          case LabTestStatus.elevated:
          case LabTestStatus.low:
            return Colors.orange;
          case LabTestStatus.other:
            return Colors.blue;
        }
      }

      expect(getStatusColor(LabTestStatus.normal), equals(Colors.green));
      expect(getStatusColor(LabTestStatus.elevated), equals(Colors.orange));
      expect(getStatusColor(LabTestStatus.low), equals(Colors.orange));
      expect(getStatusColor(LabTestStatus.other), equals(Colors.blue));
    });

    test('Status text mapping should work correctly', () {
      // Тестируем маппинг текста статусов
      // Поскольку методы приватные, тестируем логику отдельно
      
      String getStatusText(LabTestStatus status) {
        switch (status) {
          case LabTestStatus.normal:
            return 'Норма';
          case LabTestStatus.elevated:
            return 'Повышен';
          case LabTestStatus.low:
            return 'Понижен';
          case LabTestStatus.other:
            return 'Прочее';
        }
      }

      expect(getStatusText(LabTestStatus.normal), equals('Норма'));
      expect(getStatusText(LabTestStatus.elevated), equals('Повышен'));
      expect(getStatusText(LabTestStatus.low), equals('Понижен'));
      expect(getStatusText(LabTestStatus.other), equals('Прочее'));
    });

    testWidgets('Error state should be displayed correctly', (WidgetTester tester) async {
      // Создаем тест с невалидным ID для имитации ошибки
      final invalidTest = LabTest(
        id: 'invalid_id',
        name: 'Невалидный тест',
        status: LabTestStatus.normal,
        interpretationReasons: [],
        performedAt: '2025-01-15',
        value: LabTestValue(value: '0', unitLabel: ''),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: LabTestDetailScreen(labTest: invalidTest),
        ),
      );

      // Ждем завершения загрузки (которая завершится ошибкой)
      await tester.pumpAndSettle();

      // Проверяем, что экран создается без краша
      expect(find.byType(LabTestDetailScreen), findsOneWidget);
    });
  });
}


