import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:medstata_app/medstata_api.dart';
import 'mocks/mock_lab_report_service.dart';

void main() {
  group('LabReportsScreen Tests', () {
    late MockLabReportService mockService;

    setUp(() {
      mockService = MockLabReportService();
      // Устанавливаем тестовые данные
      mockService.setMockReports(MockLabReportService.createTestReports());
    });

    testWidgets('LabReportsScreen should build without errors', (WidgetTester tester) async {
      // Создаем тестовое приложение с экраном и мок-сервисом
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: LabReportsScreen(labReportService: mockService),
          ),
        ),
      );

      // Ждем завершения загрузки
      await tester.pump();
      await tester.pump(const Duration(milliseconds: 200));

      // Проверяем, что экран загружается
      expect(find.byType(LabReportsScreen), findsOneWidget);

      // Проверяем наличие фильтров
      expect(find.text('Все документы'), findsOneWidget);
      expect(find.text('По дате добавления'), findsOneWidget);
    });

    testWidgets('LabReportsScreen should show loading indicator initially', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: const LabReportsScreen(),
          ),
        ),
      );

      // Проверяем, что показывается индикатор загрузки
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('Filter dropdowns should be interactive', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: const LabReportsScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Проверяем наличие dropdown'ов (не взаимодействуем с ними, чтобы избежать краша)
      expect(find.byType(DropdownButton<String>), findsWidgets);

      // Проверяем, что есть текст фильтров
      expect(find.text('Все документы'), findsOneWidget);
      expect(find.text('По дате добавления'), findsOneWidget);
    });

    test('LabReport model should format dates correctly', () {
      final report = LabReport(
        id: 'test_id',
        performedAt: '2025-04-15',
        createdAt: '2025-04-10T14:30:00Z',
        status: LabReportStatus.completed,
        testsCount: 5,
      );

      expect(report.formattedPerformedDate, equals('15 апр 2025'));
      expect(report.formattedFullDate, equals('15.04.2025'));
      expect(report.formattedCreatedDate, equals('10 апр 2025'));
    });

    test('LabReport model should handle display titles correctly', () {
      final reportWithAnalysisName = LabReport(
        id: 'test1',
        analysisName: 'Биохимические исследования крови',
        performedAt: '2025-04-15',
        createdAt: '2025-04-10T10:00:00Z',
        status: LabReportStatus.completed,
        testsCount: 5,
      );

      final reportWithSpecimen = LabReport(
        id: 'test2',
        specimenMaterial: 'сыворотка крови',
        performedAt: '2025-04-15',
        createdAt: '2025-04-11T11:00:00Z',
        status: LabReportStatus.completed,
        testsCount: 3,
      );

      final reportMinimal = LabReport(
        id: 'test3_long_id_here',
        performedAt: '2025-04-15',
        createdAt: '2025-04-12T12:00:00Z',
        status: LabReportStatus.completed,
        testsCount: 1,
      );

      expect(reportWithAnalysisName.analysisName, equals('Биохимические исследования крови'));
      expect(reportWithSpecimen.specimenMaterial, equals('сыворотка крови'));
      expect(reportMinimal.id, startsWith('test3_long_id_here'));
    });

    test('LabReport model should handle laboratory info correctly', () {
      final reportWithLab = LabReport(
        id: 'test1',
        performedAt: '2025-04-15',

        createdAt: '2025-01-10T10:00:00Z',
        status: LabReportStatus.completed,
        testsCount: 5,
        laboratoryName: 'Инвитро AstraLab',
        doctorName: 'Альтшулер Б. Ю.',
        clinicName: 'КДЛ',
      );

      final reportMinimal = LabReport(
        id: 'test2',
        performedAt: '2025-04-15',

        createdAt: '2025-01-10T10:00:00Z',
        status: LabReportStatus.completed,
        testsCount: 3,
      );

      expect(reportWithLab.laboratoryName, equals('Инвитро AstraLab'));
      expect(reportWithLab.doctorName, equals('Альтшулер Б. Ю.'));
      expect(reportWithLab.clinicName, equals('КДЛ'));

      expect(reportMinimal.laboratoryName, isNull);
      expect(reportMinimal.doctorName, isNull);
      expect(reportMinimal.clinicName, isNull);
    });

    test('LabReport status properties should work correctly', () {
      final completedReport = LabReport(
        id: 'completed',
        performedAt: '2025-04-15',
        createdAt: '2025-04-10T10:00:00Z',
        status: LabReportStatus.completed,
        testsCount: 5,
      );

      final processingReport = LabReport(
        id: 'processing',
        performedAt: '2025-04-15',
        createdAt: '2025-04-11T11:00:00Z',
        status: LabReportStatus.processing,
        testsCount: 3,
      );

      final failedReport = LabReport(
        id: 'failed',
        performedAt: '2025-04-15',
        createdAt: '2025-04-12T12:00:00Z',
        status: LabReportStatus.failed,
        testsCount: 0,
      );

      expect(completedReport.isCompleted, isTrue);
      expect(completedReport.isProcessing, isFalse);
      expect(completedReport.isFailed, isFalse);

      expect(processingReport.isCompleted, isFalse);
      expect(processingReport.isProcessing, isTrue);
      expect(processingReport.isFailed, isFalse);

      expect(failedReport.isCompleted, isFalse);
      expect(failedReport.isProcessing, isFalse);
      expect(failedReport.isFailed, isTrue);
    });
  });
}
