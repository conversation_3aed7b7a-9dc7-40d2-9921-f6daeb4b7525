import 'package:flutter/material.dart';
import '../medstata_api.dart';

class LabReportsScreen extends StatefulWidget {
  final LabReportService? labReportService;

  const LabReportsScreen({super.key, this.labReportService});

  @override
  State<LabReportsScreen> createState() => _LabReportsScreenState();
}

class _LabReportsScreenState extends State<LabReportsScreen> {
  late final LabReportService _labReportService;
  List<LabReport>? _labReports;
  bool _isLoading = true;
  String? _errorMessage;
  String _selectedFilter = 'Все документы';
  String _selectedSort = 'По дате добавления';

  @override
  void initState() {
    super.initState();
    _labReportService = widget.labReportService ?? LabReportService();
    _loadLabReports();
  }

  @override
  void dispose() {
    _labReportService.dispose();
    super.dispose();
  }

  Future<void> _loadLabReports() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final response = await _labReportService.getLabReports();

      if (response.isSuccess) {
        setState(() {
          _labReports = response.data;
          _isLoading = false;
        });
      } else if (response.isNoData) {
        setState(() {
          _errorMessage = 'Нет загруженных документов';
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = response.message ?? 'Неизвестная ошибка';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Ошибка при загрузке документов: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildFilters(),
        Expanded(child: _buildBody()),
      ],
    );
  }

  Widget _buildFilters() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: _buildFilterDropdown(
              value: _selectedFilter,
              items: ['Все документы', 'Обработанные', 'В обработке', 'С ошибками'],
              onChanged: (value) {
                setState(() {
                  _selectedFilter = value!;
                });
              },
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildFilterDropdown(
              value: _selectedSort,
              items: ['По дате добавления', 'По дате выполнения', 'По названию', 'По статусу'],
              onChanged: (value) {
                setState(() {
                  _selectedSort = value!;
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterDropdown({
    required String value,
    required List<String> items,
    required ValueChanged<String?> onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: value,
          isExpanded: true,
          icon: const Icon(Icons.keyboard_arrow_down, size: 20),
          style: const TextStyle(
            fontSize: 14,
            color: Colors.black87,
            fontWeight: FontWeight.w500,
          ),
          items: items.map((String item) {
            return DropdownMenuItem<String>(
              value: item,
              child: Text(item),
            );
          }).toList(),
          onChanged: onChanged,
        ),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: Colors.red),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadLabReports,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Повторить'),
            ),
          ],
        ),
      );
    }

    if (_labReports == null || _labReports!.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.description_outlined,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'Нет загруженных документов',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      physics: const BouncingScrollPhysics(),
      itemCount: _getFilteredReports().length,
      itemBuilder: (context, index) {
        final report = _getFilteredReports()[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: _buildReportCard(report),
        );
      },
    );
  }

  List<LabReport> _getFilteredReports() {
    if (_labReports == null) return [];

    List<LabReport> filtered = List.from(_labReports!);

    // Фильтрация по статусу
    switch (_selectedFilter) {
      case 'Обработанные':
        filtered = filtered.where((r) => r.isCompleted).toList();
        break;
      case 'В обработке':
        filtered = filtered.where((r) => r.isProcessing).toList();
        break;
      case 'С ошибками':
        filtered = filtered.where((r) => r.isFailed).toList();
        break;
    }

    // Сортировка
    switch (_selectedSort) {
      case 'По дате добавления':
        filtered.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case 'По дате выполнения':
        filtered.sort((a, b) => b.performedAt.compareTo(a.performedAt));
        break;
      case 'По названию':
        filtered.sort((a, b) =>
            (a.analysisName ?? '').toLowerCase().compareTo((b.analysisName ?? '').toLowerCase()));
        break;
      case 'По статусу':
        filtered.sort((a, b) => a.status.value.compareTo(b.status.value));
        break;
    }

    return filtered;
  }

  Widget _buildReportCard(LabReport report) {
    return GestureDetector(
      onTap: () => _openReportDetail(report),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
          // Иконка документа
          _buildDocumentIcon(report),
          const SizedBox(width: 16),
          // Информация о документе
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  report.formattedPerformedDate,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _getDisplayTitle(report),
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          // Статус и действия
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              _buildStatusIndicator(report),
              const SizedBox(height: 8),
              if (report.isCompleted) _buildActionButtons(report),
            ],
          ),
        ],
        ),
      ),
    );
  }

  void _openReportDetail(LabReport report) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => LabReportDetailScreen(
          reportId: report.id,
        ),
      ),
    );
  }

  Widget _buildDocumentIcon(LabReport report) {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        Icons.description,
        color: Colors.grey.shade600,
        size: 24,
      ),
    );
  }

  Widget _buildStatusIndicator(LabReport report) {
    Color color;
    IconData icon;

    switch (report.status) {
      case LabReportStatus.completed:
        color = Colors.green;
        icon = Icons.check_circle;
        break;
      case LabReportStatus.processing:
      case LabReportStatus.pending:
        color = Colors.orange;
        icon = Icons.access_time;
        break;
      case LabReportStatus.failed:
        color = Colors.red;
        icon = Icons.error;
        break;
    }

    return Icon(
      icon,
      color: color,
      size: 20,
    );
  }

  Widget _buildActionButtons(LabReport report) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: Colors.red.shade50,
            borderRadius: BorderRadius.circular(8),
          ),
          child: IconButton(
            onPressed: () => _deleteReport(report),
            icon: Icon(
              Icons.delete_outline,
              color: Colors.red.shade600,
              size: 16,
            ),
            padding: EdgeInsets.zero,
          ),
        ),
        const SizedBox(width: 8),
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(8),
          ),
          child: IconButton(
            onPressed: () => _shareReport(report),
            icon: Icon(
              Icons.share_outlined,
              color: Colors.blue.shade600,
              size: 16,
            ),
            padding: EdgeInsets.zero,
          ),
        ),
      ],
    );
  }

  String _getDisplayTitle(LabReport report) {
    if (report.analysisName != null && report.analysisName!.isNotEmpty) {
      return report.analysisName!;
    }

    // Если нет названия анализа, показываем материал образца
    if (report.specimenMaterial != null && report.specimenMaterial!.isNotEmpty) {
      return report.specimenMaterial!;
    }

    // Если ничего нет, показываем ID
    return 'Анализ ${report.id.substring(0, 8)}...';
  }



  void _deleteReport(LabReport report) {
    // TODO: Implement delete functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Удаление документа: ${_getDisplayTitle(report)}'),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _shareReport(LabReport report) {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Поделиться документом: ${_getDisplayTitle(report)}'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}
