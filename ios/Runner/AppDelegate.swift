import Flutter
import UIKit
import AuthenticationServices

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    let controller : FlutterViewController = window?.rootViewController as! FlutterViewController

    // Настраиваем канал для Telegram WebApp
    let telegramChannel = FlutterMethodChannel(name: "telegram_webapp",
                                              binaryMessenger: controller.binaryMessenger)

    telegramChannel.setMethodCallHandler({
      (call: FlutterMethodCall, result: @escaping FlutterResult) -> Void in

      switch call.method {
      case "getTelegramData":
        self.getTelegramData(result: result)
      case "isTelegramWebAppAvailable":
        self.isTelegramWebAppAvailable(result: result)
      default:
        result(FlutterMethodNotImplemented)
      }
    })

    // Настраиваем канал для Apple Sign In
    let appleChannel = FlutterMethodChannel(name: "apple_signin",
                                           binaryMessenger: controller.binaryMessenger)

    appleChannel.setMethodCallHandler({
      (call: FlutterMethodCall, result: @escaping FlutterResult) -> Void in

      switch call.method {
      case "signIn":
        self.performAppleSignIn(result: result)
      case "getAuthorizationStatus":
        self.getAppleAuthorizationStatus(result: result)
      default:
        result(FlutterMethodNotImplemented)
      }
    })

    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }

  private func getTelegramData(result: @escaping FlutterResult) {
    // Проверяем, запущено ли приложение из Telegram
    if let url = self.launchURL {
      let telegramData = parseTelegramURL(url: url)
      result(telegramData)
    } else {
      // Проверяем UserDefaults или другие источники данных Telegram
      let telegramData = getTelegramDataFromUserDefaults()
      result(telegramData)
    }
  }

  private func isTelegramWebAppAvailable(result: @escaping FlutterResult) {
    // Проверяем, доступен ли Telegram WebApp
    let isAvailable = checkTelegramWebAppAvailability()
    result(isAvailable)
  }

  private var launchURL: URL? = nil

  override func application(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey : Any] = [:]) -> Bool {
    launchURL = url
    return super.application(app, open: url, options: options)
  }

  private func parseTelegramURL(url: URL) -> [String: Any]? {
    // Парсим URL от Telegram WebApp
    guard let components = URLComponents(url: url, resolvingAgainstBaseURL: false),
          let queryItems = components.queryItems else {
      return nil
    }

    var telegramData: [String: Any] = [:]

    for item in queryItems {
      if let value = item.value {
        switch item.name {
        case "tgWebAppData":
          // Декодируем данные Telegram WebApp
          if let data = value.data(using: .utf8),
             let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
            telegramData = json
          }
        case "tgWebAppStartParam":
          telegramData["startParam"] = value
        default:
          telegramData[item.name] = value
        }
      }
    }

    return telegramData.isEmpty ? nil : telegramData
  }

  private func getTelegramDataFromUserDefaults() -> [String: Any]? {
    // Проверяем сохраненные данные Telegram
    let userDefaults = UserDefaults.standard

    if let telegramData = userDefaults.object(forKey: "TelegramWebAppData") as? [String: Any] {
      return telegramData
    }

    return nil
  }

  private func checkTelegramWebAppAvailability() -> Bool {
    // Проверяем различные индикаторы наличия Telegram WebApp

    // 1. Проверяем URL схему запуска
    if launchURL?.scheme?.contains("telegram") == true {
      return true
    }

    // 2. Проверяем UserDefaults
    if UserDefaults.standard.object(forKey: "TelegramWebAppData") != nil {
      return true
    }

    // 3. Проверяем переменные окружения или другие индикаторы
    if ProcessInfo.processInfo.environment["TELEGRAM_WEBAPP"] != nil {
      return true
    }

    return false
  }

  // MARK: - Apple Sign In Methods

  private func performAppleSignIn(result: @escaping FlutterResult) {
    if #available(iOS 13.0, *) {
      let appleIDProvider = ASAuthorizationAppleIDProvider()
      let request = appleIDProvider.createRequest()
      request.requestedScopes = [.fullName, .email]

      let authorizationController = ASAuthorizationController(authorizationRequests: [request])
      authorizationController.delegate = self
      authorizationController.presentationContextProvider = self

      // Сохраняем result для использования в delegate методах
      self.appleSignInResult = result

      authorizationController.performRequests()
    } else {
      result(FlutterError(code: "UNAVAILABLE", message: "Apple Sign In недоступен на iOS < 13.0", details: nil))
    }
  }

  private func getAppleAuthorizationStatus(result: @escaping FlutterResult) {
    if #available(iOS 13.0, *) {
      let appleIDProvider = ASAuthorizationAppleIDProvider()
      // В реальном приложении здесь нужен userIdentifier от предыдущей авторизации
      appleIDProvider.getCredentialState(forUserID: "test_user_id") { (credentialState, error) in
        DispatchQueue.main.async {
          switch credentialState {
          case .authorized:
            result(true)
          case .revoked, .notFound:
            result(false)
          default:
            result(false)
          }
        }
      }
    } else {
      result(false)
    }
  }

  // Переменная для хранения Flutter result
  private var appleSignInResult: FlutterResult?
}

// MARK: - ASAuthorizationControllerDelegate

@available(iOS 13.0, *)
extension AppDelegate: ASAuthorizationControllerDelegate {
  func authorizationController(controller: ASAuthorizationController, didCompleteWithAuthorization authorization: ASAuthorization) {
    if let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential {

      var appleData: [String: Any] = [:]

      // Identity Token (обязательный)
      if let identityTokenData = appleIDCredential.identityToken,
         let identityToken = String(data: identityTokenData, encoding: .utf8) {
        appleData["identityToken"] = identityToken
      }

      // Authorization Code
      if let authorizationCodeData = appleIDCredential.authorizationCode,
         let authorizationCode = String(data: authorizationCodeData, encoding: .utf8) {
        appleData["authorizationCode"] = authorizationCode
      }

      // User Identifier
      appleData["userIdentifier"] = appleIDCredential.user

      // Email (может быть nil при повторных авторизациях)
      if let email = appleIDCredential.email {
        appleData["email"] = email
      }

      // Full Name (может быть nil при повторных авторизациях)
      if let fullName = appleIDCredential.fullName {
        if let givenName = fullName.givenName {
          appleData["firstName"] = givenName
        }
        if let familyName = fullName.familyName {
          appleData["lastName"] = familyName
        }
      }

      appleSignInResult?(appleData)
      appleSignInResult = nil
    } else {
      appleSignInResult?(FlutterError(code: "INVALID_CREDENTIAL", message: "Получены неверные данные авторизации", details: nil))
      appleSignInResult = nil
    }
  }

  func authorizationController(controller: ASAuthorizationController, didCompleteWithError error: Error) {
    let errorMessage: String

    if let authError = error as? ASAuthorizationError {
      switch authError.code {
      case .canceled:
        errorMessage = "Авторизация отменена пользователем"
      case .failed:
        errorMessage = "Авторизация не удалась"
      case .invalidResponse:
        errorMessage = "Неверный ответ от Apple"
      case .notHandled:
        errorMessage = "Авторизация не обработана"
      case .unknown:
        errorMessage = "Неизвестная ошибка авторизации"
      default:
        errorMessage = "Ошибка авторизации: \(authError.localizedDescription)"
      }
    } else {
      errorMessage = "Ошибка авторизации: \(error.localizedDescription)"
    }

    appleSignInResult?(FlutterError(code: "AUTHORIZATION_ERROR", message: errorMessage, details: nil))
    appleSignInResult = nil
  }
}

// MARK: - ASAuthorizationControllerPresentationContextProviding

@available(iOS 13.0, *)
extension AppDelegate: ASAuthorizationControllerPresentationContextProviding {
  func presentationAnchor(for controller: ASAuthorizationController) -> ASPresentationAnchor {
    return self.window!
  }
}
